//  PetItemView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/17.
//

import SwiftUI
import SwiftData

struct PetItemView: View {
    let displayModel: PetDisplayModel
    @EnvironmentObject var petViewModel: CarbonPetViewModel

    // SwiftData 环境
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \CarbonCoin.id, order: .forward) private var coins: [CarbonCoin]
    private var currentAmount: Int { coins.first?.amount ?? 0 }

    private let cardWidth: CGFloat = 160
    private let cardHeight: CGFloat = 170
    @State private var isAnimating = false
    @State private var showAcquisitionSheet = false

    @Namespace private var namespace

    var body: some View {
        // 根据是否获得决定点击行为
        if displayModel.isOwned {
            // 已获得：跳转到详情页面
            NavigationLink(
                destination: PetDetailView(displayModel: displayModel)
                    .navigationTransition(.zoom(sourceID: displayModel.id, in: namespace))
            ) {
                petCardContent
            }
            .buttonStyle(PlainButtonStyle())
        } else {
            // 未获得：显示获得条件的 sheet
            Button(action: {
                showAcquisitionSheet = true
            }) {
                petCardContent
            }
            .buttonStyle(PlainButtonStyle())
            .sheet(isPresented: $showAcquisitionSheet) {
                PetAcquisitionSheet(
                    displayModel: displayModel,
                    currentAmount: currentAmount,
                    onPurchase: handlePetPurchase
                )
            }
        }
    }

    // 宠物卡片内容
    private var petCardContent: some View {
        ZStack(alignment: .bottom) {
            // MARK: 背景和背景图片
            ZStack {
                RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                    .fill(LinearGradient.petItemBgColor)
                    .frame(width: cardWidth, height: cardHeight)
                    .shadow(color: .black.opacity(0.3), radius: 10, y: 5)

                if (displayModel.isOwned ){
                    if let tag_bg = UIImage(named: "level_tag_bg") {
                        Image(uiImage: tag_bg)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 40)
                            .offset(x:20, y: -cardHeight * 0.25)
                    }
                }

            }
            .clipShape(RoundedRectangle(cornerRadius: Theme.CornerRadius.lg))
            .glassCard()

            // 宠物图片（允许超出边界）
            if let petImage = UIImage(named: displayModel.template.imageName) {
                Image(uiImage: petImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: cardWidth * 1.05, height: cardHeight * 1.05)
                    .offset(y: isAnimating ? -cardHeight * 0.06 : -cardHeight * 0.04) // 向上偏移，使其突出
                    .opacity(isAnimating ? 1:0.3)
                    .animation(
                        Animation
                            .easeInOut(duration: 0.5),

                        value: isAnimating)                        // 如果未获得，将图片转换为黑白
                    .colorMultiply(!displayModel.isOwned ? .black.opacity(0.5) : .white)
            }

            // 等级标签
            if( displayModel.isOwned ){
                levelTag
                    .offset(x: cardWidth * 0.30, y: -cardHeight * 0.25)
                    .frame(width: cardWidth, height: cardHeight)
            }

            // 底部信息栏
            bottomInfoBar
        }
        .frame(width: cardWidth, height: cardHeight)
        // 添加遮罩：如果未获得，整个卡片变灰
        .overlay(
           !displayModel.isOwned ?
           Color.black.opacity(0.3)
               .clipShape(RoundedRectangle(cornerRadius: Theme.CornerRadius.lg))
               .frame(width: cardWidth, height: cardHeight)
           : nil
        )
        .advancedCardButtonStyle()
        .background(Color.clear)
        .onAppear(perform: {
            isAnimating = true
        })
    }

    // 处理宠物购买
    private func handlePetPurchase() {
        guard case .purchase(let amount) = displayModel.template.acquisition else { return }

        // 检查金币是否足够
        if currentAmount >= amount {
            // 扣除金币
            if let coin = coins.first {
                coin.amount -= amount
            }

            // 解锁宠物
            petViewModel.unlockPet(templateName: displayModel.template.name)

            // 关闭 sheet
            showAcquisitionSheet = false
        }
    }

    
    // MARK: 等级标签
    private var levelTag: some View {
        ZStack {
            Text("Lv\(displayModel.displayLevel)")
                .font(.title2Brand)
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 5)
                .rotationEffect(.degrees(45))
        }
    }
    
    // MARK: 底部信息栏
    private var bottomInfoBar: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(displayModel.template.name)
                    .font(.subheadline)
                    .foregroundColor(.white)
                    .opacity(isAnimating ? 1: 0)
                    .animation(.easeOut(duration: 0.5),value: isAnimating)
                
                HStack(spacing: 2) {
                    ForEach(0..<displayModel.template.rarity, id: \.self) { _ in
                        Image("star")
                            .foregroundColor(.yellow)
                            .font(.system(size: 12))
                    }
                    .opacity(isAnimating ? 1: 0)
                    .animation(.easeOut(duration: 0.5),value: isAnimating)
                }
            }
            
            Spacer()

            OwnershipBadge(isOwned: displayModel.isOwned)
        }
        .padding(12)
        .background(
            Color.black.opacity(0.6)
                .blur(radius: 4)
        )
        .clipShape(RoundedCorner(radius: 20, corners: [.bottomLeft, .bottomRight]))
    }
}

// MARK: - Preview
#Preview {
    let mockTemplateOwned = PetTemplate(
        name: "小碳",
        imageName: "pet_小碳",
        rarity: 2,
        introduction: "草系小萌宠，浑身雪白，翠绿耳饰、颈叶、尾斑，小碳的圆眼睛亮闪闪，永远挂着甜甜的笑容，性格活泼又热情，会陪着你用碳币解锁各种互动玩法。 小碳活力满满，跑向宝藏的速度飞快，能够发现更多路上的东西。",
        acquisition: .purchase(amount: 100)
    )
    let mockUserPet = UserPet(templateName: "小碳", level: 5, experience: 250)
    let mockDisplayModelOwned = PetDisplayModel(template: mockTemplateOwned, userPet: mockUserPet)

    let mockTemplateNotOwned = PetTemplate(
        name: "贰负",
        imageName: "pet_贰负",
        rarity: 3,
        introduction: "贰负龙身而龟背，看上去总是活力满满，尽管他是个储水高手，却对用水量把控的异常严格。",
        acquisition: .purchase(amount: 200)
    )
    let mockDisplayModelNotOwned = PetDisplayModel(template: mockTemplateNotOwned, userPet: nil)

    return ZStack {
        Color.black.ignoresSafeArea()
        VStack(spacing: 20) {
            PetItemView(displayModel: mockDisplayModelOwned)
            PetItemView(displayModel: mockDisplayModelNotOwned)
        }
    }
    .environmentObject(CarbonPetViewModel())
    .modelContainer(for: CarbonCoin.self)
}

// MARK: - 宠物获得条件 Sheet
struct PetAcquisitionSheet: View {
    let displayModel: PetDisplayModel
    let currentAmount: Int
    let onPurchase: () -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: Theme.Spacing.lg) {
                // 宠物灰色图像
                petImageSection

                // 宠物信息
                petInfoSection

                // 获得条件
                acquisitionConditionSection

                Spacer()

                // 获得宠物按钮
                acquisitionButton
            }
            .padding(Theme.Spacing.lg)
            .navigationTitle("获得宠物")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    // 宠物图像区域
    private var petImageSection: some View {
        ZStack {
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(LinearGradient.petItemBgColor)
                .frame(width: 200, height: 200)
                .shadow(color: .black.opacity(0.3), radius: 10, y: 5)

            if let petImage = UIImage(named: displayModel.template.imageName) {
                Image(uiImage: petImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 180, height: 180)
                    .colorMultiply(.black.opacity(0.5)) // 灰色效果
            }
        }
        .glassCard()
    }

    // 宠物信息区域
    private var petInfoSection: some View {
        VStack(spacing: Theme.Spacing.sm) {
            Text(displayModel.template.name)
                .font(.title2Brand)
                .foregroundColor(.textPrimary)

            HStack(spacing: 2) {
                ForEach(0..<displayModel.template.rarity, id: \.self) { _ in
                    Image("star")
                        .foregroundColor(.yellow)
                        .font(.system(size: 16))
                }
            }

            Text(displayModel.template.introduction)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .glassCard()
    }

    // 获得条件区域
    private var acquisitionConditionSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("获得条件")
                .font(.headline)
                .foregroundColor(.textPrimary)

            switch displayModel.template.acquisition {
            case .purchase(let amount):
                HStack {
                    Image(systemName: "leaf.fill")
                        .foregroundColor(.brandGreen)
                    Text("需要碳币: \(amount)")
                        .font(.bodyBrand)
                    Spacer()
                    Text("当前: \(currentAmount)")
                        .font(.bodyBrand)
                        .foregroundColor(currentAmount >= amount ? .success : .error)
                }

            case .loginDays(let days):
                HStack {
                    Image(systemName: "calendar.badge.clock")
                        .foregroundColor(.info)
                    Text("需要连续登录: \(days) 天")
                        .font(.bodyBrand)
                    Spacer()
                    // TODO: 实现登录天数检查
                    Text("未实现")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }

            case .task(let taskId):
                HStack {
                    Image(systemName: "checkmark.circle")
                        .foregroundColor(.brandColor)
                    Text("完成指定任务: \(taskId)")
                        .font(.bodyBrand)
                    Spacer()
                    // TODO: 实现任务完成检查
                    Text("未实现")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }
            }
        }
        .padding()
        .background(Color.cardBackground.opacity(0.3))
        .cornerRadius(Theme.CornerRadius.md)
    }

    // 获得宠物按钮
    private var acquisitionButton: some View {
        Button(action: {
            switch displayModel.template.acquisition {
            case .purchase(_):
                onPurchase()
            case .loginDays(_), .task(_):
                // TODO: 实现其他获得方式
                break
            }
        }) {
            HStack {
                Image(systemName: "heart.fill")
                Text("获得宠物")
                    .font(.bodyBrand)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                canAcquire ? Color.brandGreen : Color.gray
            )
            .cornerRadius(Theme.CornerRadius.md)
        }
        .disabled(!canAcquire)
    }

    // 是否可以获得
    private var canAcquire: Bool {
        switch displayModel.template.acquisition {
        case .purchase(let amount):
            return currentAmount >= amount
        case .loginDays(_), .task(_):
            // TODO: 实现其他条件检查
            return false
        }
    }
}

// Helper for specific corner radius
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}
