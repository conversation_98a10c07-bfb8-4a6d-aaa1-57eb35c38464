//
//  FootprintView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI
import SwiftData


struct FootprintView: View {
    @EnvironmentObject var healthManager: HealthManager
    @StateObject private var healthDataViewModel: HealthDataViewModel

    init() {
        // 注意：这里我们不能直接访问 @EnvironmentObject，所以先用临时实例
        // 在 onAppear 中会更新为正确的 healthManager
        self._healthDataViewModel = StateObject(wrappedValue: HealthDataViewModel(dataType: .steps, healthManager: HealthManager()))
    }

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                ScrollView {
                    VStack(spacing: Theme.Spacing.lg) {
                        UserGreetingCard()
                        MapEntryButton()
                        HealthStatisticsCard(dataType: .steps, viewModel: healthDataViewModel)
                        Spacer(minLength: 50)
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.top, Theme.Spacing.md)
                }
                .scrollContentBackground(.hidden) // 隐藏 ScrollView 默认背景
            }
            .navigationTitle("足迹")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
        .onAppear {
            healthDataViewModel.updateHealthManager(healthManager)
            Task {
                await healthDataViewModel.initializeData()
            }
        }
    }
}

// MARK: - User Greeting Card
struct UserGreetingCard: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \CarbonCoin.id, order: .forward) private var coins: [CarbonCoin]
    private var currentAmount: Int { coins.first?.amount ?? 0 }
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                Text("Hi, 用户")
                    .font(.title2Brand)
                    .foregroundColor(.textPrimary)

                HStack {
                    Image(systemName: "leaf.fill")
                        .foregroundColor(.brandGreen)

                    Text("碳币: \(currentAmount)")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                    
                    Button("增加 100") {
                        if let coin = coins.first {
                            coin.amount += 100
                        } else {
                            modelContext.insert(CarbonCoin(amount: 100))
                        }
                    }

                    Spacer()

                    HStack(spacing: 4) {
                        Text("↗ 12%")
                            .font(.captionBrand)
                            .foregroundColor(.success)
                    }
                }
            }

            Spacer()
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}



// MARK: - Map Entry Button
struct MapEntryButton: View {
    @State private var showMapView = false
    private let cardHeight: CGFloat = 142

    var body: some View {
        Button(action: {
            showMapView = true
        }) {
            ZStack(alignment: .bottom) {  // 将内容对齐到底部
                // 背景卡片（半透明黑色，确保文字清晰）
                RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                    .fill(Color.cardBackground.opacity(0.8))
                    .frame(height: cardHeight)  // 调整高度以适应内容

                // 底部装饰图片（地球图，紧贴底部）
                VStack {
                    Spacer()  // 将图片推到底部
                    Image("MapCover")
                        .resizable()
                        .scaledToFit()
                        .frame(height: 108)  // 控制图片大小
                }
                .frame(height: cardHeight)

                // 主要内容（文字 + 按钮）
                VStack(spacing: Theme.Spacing.sm) {
                    // 标题文字（居中）
                    Text("碳迹地图已经准备就绪！")
                        .font(.title3Brand)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, Theme.Spacing.lg)
                        .padding(.top)

                    Spacer()

                    // 按钮（居中，渐变色）
                    Text("点击开启")
                        .font(.bodyBrand)
                        .foregroundColor(.black)
                        .padding(.horizontal, Theme.Spacing.lg)
                        .padding(.vertical, Theme.Spacing.md)
                        .background(
                            LinearGradient(
                                colors: [Color(hex: "61D7B9"), Color(hex: "B0EB67")],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .clipShape(Capsule())
                        .padding(.bottom, 0)
                }
                .padding(.bottom, 24)  // 确保内容不被图片遮挡
            }
        }
        .glassCard()
        .buttonStyle(PlainButtonStyle())  // 移除默认按钮样式
        .advancedCardButtonStyle()  // 应用自定义按压动画
        .fullScreenCover(isPresented: $showMapView) {
            MapView()
        }
    }
}



// MARK: - Map Placeholder View
struct MapPlaceholderView: View {
    var body: some View {
        ZStack {
            CustomAngularGradient()

            VStack(spacing: Theme.Spacing.xl) {
                Image("MapCover")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 200, height: 150)
                    .opacity(0.8)

                VStack(spacing: Theme.Spacing.md) {
                    Text("碳迹地图")
                        .font(.title2Brand)
                        .foregroundColor(.textPrimary)

                    Text("地图功能正在开发中...")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
        }
        .navigationTitle("碳迹地图")
        .navigationBarTitleDisplayMode(.large)
        .toolbarBackground(.clear, for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
    }
}

#Preview {
    let healthManager = HealthManager()
    FootprintView()
        .stableBackground()
        .environmentObject(healthManager)
}

