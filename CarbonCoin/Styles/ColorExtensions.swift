//
//  ColorExtensions.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

// MARK: - Color Extensions
extension Color {
    
    // MARK: - Brand Colors
    /// 主品牌色
    static let brandColor = Color("brandColor")

    static let brandGreen = Color("CCGreen")

    // MARK: - New Design System Colors
    /// 默认按钮背景色 #242720
    static let defaultButtonBackground = Color(hex: "242720")

    /// 卡片背景色 #1F1F1F
    static let cardBackgroundColor = Color(hex: "1F1F1F")
    
    // MARK: - Gradient Colors
    /// 主渐变色 - 按钮选中状态：从品牌绿到辅助黄 (180deg, #61D7B9 0%, #B0EB67 100%)
    static let primaryGradient = LinearGradient(
        gradient: Gradient(colors: [skyBlue, brandGreen]),
        startPoint: .top,
        endPoint: .bottom
    )

    /// 反向主渐变色：从辅助黄到品牌绿
    static let primaryGradientReversed = LinearGradient(
        gradient: Gradient(colors: [auxiliaryYellow, brandGreen]),
        startPoint: .top,
        endPoint: .bottom
    )

    /// 水平主渐变色：从左到右
    static let primaryGradientHorizontal = LinearGradient(
        gradient: Gradient(colors: [brandGreen, auxiliaryYellow]),
        startPoint: .leading,
        endPoint: .trailing
    )

    /// 对角线渐变色：从左上到右下
    static let primaryGradientDiagonal = LinearGradient(
        gradient: Gradient(colors: [brandGreen, auxiliaryYellow]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    /// 天空渐变色：从天空蓝到品牌绿
    static let skyGradient = LinearGradient(
        gradient: Gradient(colors: [skyBlue, brandGreen]),
        startPoint: .top,
        endPoint: .bottom
    )

    // MARK: - New Design System Gradients
    /// 全局背景圆锥渐变 - 模拟圆锥渐变效果
    static let globalBackgroundGradient = AngularGradient(
        gradient: Gradient(stops: [
            .init(color: Color(hex: "010101"), location: 0.00),
            .init(color: Color(hex: "000000"), location: 0.0048),
            .init(color: Color(hex: "2E4F17"), location: 0.3699),
            .init(color: Color(hex: "2A370C"), location: 0.5240), // 188.65/360 ≈ 0.5240
            .init(color: Color(hex: "010101"), location: 1.0)
        ]),
        center: UnitPoint(x: 0.4281, y: 0.4891),
        angle: Angle(degrees: 189.17)
    )


    /// 卡片边框渐变 - 180度线性渐变
    static let cardBorderGradient = LinearGradient(
        gradient: Gradient(stops: [
            .init(color: Color.white.opacity(0.5), location: 0.0),
            .init(color: Color(hex: "4B7905").opacity(0.5), location: 0.31),
            .init(color: Color(hex: "FDFF81").opacity(0.5), location: 0.67),
            .init(color: Color(hex: "A8D200").opacity(0.5), location: 0.90)
        ]),
        startPoint: .topTrailing,
        endPoint: .bottomLeading
    )
    
    // MARK: - UI Colors

    /// 卡片背景色 - 新设计系统卡片背景色 #1F1F1F
    static let cardBackground = cardBackgroundColor

    /// 玻璃效果背景色 - 保持半透明效果
    static let glassBackground = Color.white.opacity(0.15)
    
    /// 文字主色 - 白色
    static let textPrimary = Color.white
    
    /// 文字次要色 - 半透明白色
    static let textSecondary = Color.white.opacity(0.7)
    
    /// 文字禁用色 - 更透明的白色
    static let textDisabled = Color.white.opacity(0.4)
    
    /// 反转色
    static let textInvert = Color.black
    
    // MARK: - Status Colors
    /// 成功色 - 绿色
    static let success = Color.green
    
    /// 警告色 - 橙色
    static let warning = Color.orange
    
    /// 错误色 - 红色
    static let error = Color.red
    
    /// 信息色 - 蓝色
    static let info = Color.blue
}

// MARK: - Gradient Extensions
extension LinearGradient {
    
    /// 创建角度渐变
    /// - Parameters:
    ///   - colors: 渐变颜色数组
    ///   - angle: 角度（度数）
    /// - Returns: 线性渐变
    static func angle(_ colors: [Color], angle: Double) -> LinearGradient {
        let radians = angle * .pi / 180
        let x = cos(radians)
        let y = sin(radians)
        
        return LinearGradient(
            gradient: Gradient(colors: colors),
            startPoint: UnitPoint(x: 0.5 - x/2, y: 0.5 - y/2),
            endPoint: UnitPoint(x: 0.5 + x/2, y: 0.5 + y/2)
        )
    }
    
    /// 主品牌渐变 - 180度
    static let primaryBrand = LinearGradient.angle([.brandGreen, .auxiliaryYellow], angle: 180)
    
    /// 主品牌渐变 - 45度对角线
    static let primaryBrandDiagonal = LinearGradient.angle([.brandGreen, .auxiliaryYellow], angle: 45)
    
    /// 宠物卡片的渐变色背景
    static var petItemBgColor: LinearGradient {
        LinearGradient(
            stops: [
                .init(color: Color(hex: "409876"), location: 0),
                .init(color: Color(hex: "446F39"), location: 0.19),
                .init(color: Color(hex: "344B2D"), location: 0.35),
                .init(color: Color(hex: "242720"), location: 0.59)
            ],
            startPoint: .topTrailing,
            endPoint: .bottomLeading
        )
    }
}

// MARK: - Shadow Extensions
extension View {
    
    /// 应用主要阴影效果
    func primaryShadow() -> some View {
        self.shadow(
            color: Color.black.opacity(0.2),
            radius: 8,
            x: 0,
            y: 4
        )
    }
    
    /// 应用卡片阴影效果
    func cardShadow() -> some View {
        self.shadow(
            color: Color.black.opacity(0.1),
            radius: 4,
            x: 0,
            y: 2
        )
    }
    
    /// 应用浮动阴影效果
    func floatingShadow() -> some View {
        self.shadow(
            color: Color.black.opacity(0.3),
            radius: 12,
            x: 0,
            y: 6
        )
    }
}
